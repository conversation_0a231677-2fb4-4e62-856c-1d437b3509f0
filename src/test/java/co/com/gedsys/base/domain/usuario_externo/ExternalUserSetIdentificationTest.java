package co.com.gedsys.base.domain.usuario_externo;

import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.*;

@DisplayName("ExternalUser - setIdentification method")
class ExternalUserSetIdentificationTest {

    @Test
    @DisplayName("debe permitir cambio de NA a CC")
    void debePermitirCambioDeNAaCC() {
        // Arrange - Usuario con tipo NA (como en el caso real)
        ExternalUser user = new ExternalUser("Frank Jungle", "NA", null);
        
        // Act - Cambiar a CC con número (como en el request)
        assertDoesNotThrow(() -> {
            user.setIdentification(ExternalUserIdentificationType.CC, "12345678");
        });
        
        // Assert
        assertEquals(ExternalUserIdentificationType.CC, user.getIdentificationType());
        assertEquals("12345678", user.getIdentificationNumber());
    }

    @Test
    @DisplayName("debe permitir cambio de CC a NA")
    void debePermitirCambioDeCCaNA() {
        // Arrange
        ExternalUser user = new ExternalUser("<PERSON>", "CC", "87654321");
        
        // Act
        assertDoesNotThrow(() -> {
            user.setIdentification(ExternalUserIdentificationType.NA, null);
        });
        
        // Assert
        assertEquals(ExternalUserIdentificationType.NA, user.getIdentificationType());
        assertNull(user.getIdentificationNumber());
    }

    @Test
    @DisplayName("debe normalizar número a null para tipo NA")
    void debeNormalizarNumeroANullParaTipoNA() {
        // Arrange
        ExternalUser user = new ExternalUser("Test User", "CC", "123");
        
        // Act - Intentar setear NA con número (debe normalizarse a null)
        user.setIdentification(ExternalUserIdentificationType.NA, "cualquier-valor");
        
        // Assert
        assertEquals(ExternalUserIdentificationType.NA, user.getIdentificationType());
        assertNull(user.getIdentificationNumber());
    }

    @Test
    @DisplayName("debe validar que CC requiere número")
    void debeValidarQueCCRequiereNumero() {
        // Arrange
        ExternalUser user = new ExternalUser("Test User", "NA", null);
        
        // Act & Assert
        IllegalArgumentException exception = assertThrows(IllegalArgumentException.class, () -> {
            user.setIdentification(ExternalUserIdentificationType.CC, null);
        });
        
        assertTrue(exception.getMessage().contains("deben tener número de identificación"));
    }

    @Test
    @DisplayName("debe validar que CC requiere número no vacío")
    void debeValidarQueCCRequiereNumeroNoVacio() {
        // Arrange
        ExternalUser user = new ExternalUser("Test User", "NA", null);
        
        // Act & Assert
        IllegalArgumentException exception = assertThrows(IllegalArgumentException.class, () -> {
            user.setIdentification(ExternalUserIdentificationType.CC, "");
        });
        
        assertTrue(exception.getMessage().contains("deben tener número de identificación"));
    }
}
