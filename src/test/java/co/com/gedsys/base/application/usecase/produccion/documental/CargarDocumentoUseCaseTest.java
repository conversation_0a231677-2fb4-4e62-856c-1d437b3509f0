package co.com.gedsys.base.application.usecase.produccion.documental;

import co.com.gedsys.base.application.common.EntityNotExistsException;
import co.com.gedsys.base.application.dto.DocumentoDTO;
import co.com.gedsys.base.application.mapper.DocumentoApplicationLayerMapper;
import co.com.gedsys.base.application.usecase.gestion_tramite.CargarDocumentoCommand;
import co.com.gedsys.base.application.usecase.gestion_tramite.CargarDocumentoUseCase;
import co.com.gedsys.base.domain.documento.DocumentoRepository;
import co.com.gedsys.base.domain.metadato.repository.DefinicionMetadatosRepository;
import co.com.gedsys.base.domain.tipologia.TipoDocumentalRepository;
import co.com.gedsys.base.domain.unidad_documental.UnidadDocumentalRepository;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.List;
import java.util.Optional;
import java.util.Set;
import java.util.UUID;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
@DisplayName("CargarDocumentoUseCase: Casos de uso y validaciones")
class CargarDocumentoUseCaseTest {
    @Mock
    private DocumentoRepository documentoRepository;
    @Mock
    private TipoDocumentalRepository tipoDocumentalRepository;
    @Mock
    private UnidadDocumentalRepository unidadDocumentalRepository;
    @Mock
    private DefinicionMetadatosRepository definicionMetadatosRepository;
    @Mock
    private DocumentoApplicationLayerMapper mapper;
    @InjectMocks
    private CargarDocumentoUseCase useCase;

    private UUID tipoDocumentalId;
    private UUID unidadDocumentalId;

    @BeforeEach
    void setUp() {
        tipoDocumentalId = UUID.randomUUID();
        unidadDocumentalId = UUID.randomUUID();
    }

    @Nested
    @DisplayName("Escenarios exitosos")
    class EscenariosExitosos {
        @Test
        @DisplayName("Debe cargar un documento correctamente con metadatos y anexos")
        void deberiaCargarDocumentoCorrectamente() {
            var command = new CargarDocumentoCommand(
                    "Título de prueba",
                    "usuario1",
                    "file-123",
                    tipoDocumentalId,
                    unidadDocumentalId,
                    List.of(new CargarDocumentoCommand.Metadato("asunto", "valor", co.com.gedsys.base.domain.metadato.enums.TipoMetadatoEnum.CONTENIDO)),
                    List.of()
            );
            var tipoDocumental = mock(co.com.gedsys.base.domain.tipologia.TipoDocumental.class);
            var unidadDocumental = mock(co.com.gedsys.base.domain.unidad_documental.UnidadDocumental.class);
            var definicionMetadato = mock(co.com.gedsys.base.domain.metadato.DefinicionMetadato.class);
            when(tipoDocumentalRepository.findById(tipoDocumentalId)).thenReturn(Optional.of(tipoDocumental));
            when(unidadDocumentalRepository.findById(unidadDocumentalId)).thenReturn(Optional.of(unidadDocumental));
            when(definicionMetadatosRepository.buscarPorPatrones(List.of("asunto"))).thenReturn(Set.of(definicionMetadato));
            when(definicionMetadato.getPatron()).thenReturn("asunto");
            when(definicionMetadato.generarMetadato("valor")).thenReturn(mock(co.com.gedsys.base.domain.metadato.Metadato.class));
            var documentoGuardado = mock(co.com.gedsys.base.domain.documento.Documento.class);
            when(documentoRepository.save(any())).thenReturn(documentoGuardado);
            var dtoEsperado = mock(DocumentoDTO.class);
            when(mapper.toDTO(documentoGuardado)).thenReturn(dtoEsperado);

            var resultado = useCase.execute(command);
            assertNotNull(resultado);
            assertEquals(dtoEsperado, resultado);
            verify(tipoDocumentalRepository).findById(tipoDocumentalId);
            verify(unidadDocumentalRepository).findById(unidadDocumentalId);
            verify(definicionMetadatosRepository).buscarPorPatrones(List.of("asunto"));
            verify(documentoRepository).save(any());
            verify(mapper).toDTO(documentoGuardado);
        }

        @Test
        @DisplayName("Debe permitir registrar documento sin metadatos")
        void deberiaPermitirRegistrarDocumentoSinMetadatos() {
            var command = new CargarDocumentoCommand(
                    "Título",
                    "usuario1",
                    "file-123",
                    tipoDocumentalId,
                    unidadDocumentalId,
                    List.of(),
                    List.of()
            );
            when(tipoDocumentalRepository.findById(tipoDocumentalId)).thenReturn(Optional.of(mock(co.com.gedsys.base.domain.tipologia.TipoDocumental.class)));
            when(unidadDocumentalRepository.findById(unidadDocumentalId)).thenReturn(Optional.of(mock(co.com.gedsys.base.domain.unidad_documental.UnidadDocumental.class)));
            when(definicionMetadatosRepository.buscarPorPatrones(List.of())).thenReturn(Set.of());
            var documentoGuardado = mock(co.com.gedsys.base.domain.documento.Documento.class);
            when(documentoRepository.save(any())).thenReturn(documentoGuardado);
            var dtoEsperado = mock(DocumentoDTO.class);
            when(mapper.toDTO(documentoGuardado)).thenReturn(dtoEsperado);
            var resultado = useCase.execute(command);
            assertNotNull(resultado);
            assertEquals(dtoEsperado, resultado);
        }

        @Test
        @DisplayName("Debe permitir registrar documento sin anexos (anexos nulos)")
        void deberiaPermitirRegistrarDocumentoSinAnexos() {
            var command = new CargarDocumentoCommand(
                    "Título",
                    "usuario1",
                    "file-123",
                    tipoDocumentalId,
                    unidadDocumentalId,
                    List.of(),
                    null
            );
            when(tipoDocumentalRepository.findById(tipoDocumentalId)).thenReturn(Optional.of(mock(co.com.gedsys.base.domain.tipologia.TipoDocumental.class)));
            when(unidadDocumentalRepository.findById(unidadDocumentalId)).thenReturn(Optional.of(mock(co.com.gedsys.base.domain.unidad_documental.UnidadDocumental.class)));
            when(definicionMetadatosRepository.buscarPorPatrones(List.of())).thenReturn(Set.of());
            var documentoGuardado = mock(co.com.gedsys.base.domain.documento.Documento.class);
            when(documentoRepository.save(any())).thenReturn(documentoGuardado);
            var dtoEsperado = mock(DocumentoDTO.class);
            when(mapper.toDTO(documentoGuardado)).thenReturn(dtoEsperado);
            var resultado = useCase.execute(command);
            assertNotNull(resultado);
            assertEquals(dtoEsperado, resultado);
        }
    }

    @Nested
    @DisplayName("Validaciones de entidades relacionadas")
    class ValidacionesEntidadesRelacionadas {
        @Test
        @DisplayName("Debe lanzar excepción si TipoDocumental no existe")
        void deberiaLanzarExcepcionSiTipoDocumentalNoExiste() {
            var command = new CargarDocumentoCommand(
                    "Título",
                    "usuario1",
                    "file-123",
                    tipoDocumentalId,
                    unidadDocumentalId,
                    List.of(new CargarDocumentoCommand.Metadato("asunto", "valor", co.com.gedsys.base.domain.metadato.enums.TipoMetadatoEnum.CONTENIDO)),
                    List.of()
            );
            when(tipoDocumentalRepository.findById(tipoDocumentalId)).thenReturn(Optional.empty());
            assertThrows(EntityNotExistsException.class, () -> useCase.execute(command));
        }

        @Test
        @DisplayName("Debe lanzar excepción si UnidadDocumental no existe")
        void deberiaLanzarExcepcionSiUnidadDocumentalNoExiste() {
            var command = new CargarDocumentoCommand(
                    "Título",
                    "usuario1",
                    "file-123",
                    tipoDocumentalId,
                    unidadDocumentalId,
                    List.of(new CargarDocumentoCommand.Metadato("asunto", "valor", co.com.gedsys.base.domain.metadato.enums.TipoMetadatoEnum.CONTENIDO)),
                    List.of()
            );
            when(tipoDocumentalRepository.findById(tipoDocumentalId)).thenReturn(Optional.of(mock(co.com.gedsys.base.domain.tipologia.TipoDocumental.class)));
            when(unidadDocumentalRepository.findById(unidadDocumentalId)).thenReturn(Optional.empty());
            assertThrows(EntityNotExistsException.class, () -> useCase.execute(command));
        }
    }

    @Nested
    @DisplayName("Validaciones de metadatos")
    class ValidacionesMetadatos {
        @Test
        @DisplayName("Debe lanzar excepción si faltan definiciones de metadatos")
        void deberiaLanzarExcepcionSiFaltanDefinicionesDeMetadatos() {
            var command = new CargarDocumentoCommand(
                    "Título",
                    "usuario1",
                    "file-123",
                    tipoDocumentalId,
                    unidadDocumentalId,
                    List.of(new CargarDocumentoCommand.Metadato("asunto", "valor", co.com.gedsys.base.domain.metadato.enums.TipoMetadatoEnum.CONTENIDO)),
                    List.of()
            );
            when(tipoDocumentalRepository.findById(tipoDocumentalId)).thenReturn(Optional.of(mock(co.com.gedsys.base.domain.tipologia.TipoDocumental.class)));
            when(unidadDocumentalRepository.findById(unidadDocumentalId)).thenReturn(Optional.of(mock(co.com.gedsys.base.domain.unidad_documental.UnidadDocumental.class)));
            when(definicionMetadatosRepository.buscarPorPatrones(List.of("asunto"))).thenReturn(Set.of());
            assertThrows(co.com.gedsys.base.application.usecase.produccion.radicacion.exception.MetadatoNoExisteException.class, () -> useCase.execute(command));
        }

        @Test
        @DisplayName("Debe lanzar excepción si hay metadatos duplicados")
        void deberiaLanzarExcepcionSiHayMetadatosDuplicados() {
            var command = new CargarDocumentoCommand(
                    "Título",
                    "usuario1",
                    "file-123",
                    tipoDocumentalId,
                    unidadDocumentalId,
                    List.of(
                            new CargarDocumentoCommand.Metadato("asunto", "valor1", co.com.gedsys.base.domain.metadato.enums.TipoMetadatoEnum.CONTENIDO),
                            new CargarDocumentoCommand.Metadato("asunto", "valor2", co.com.gedsys.base.domain.metadato.enums.TipoMetadatoEnum.GESTION)
                    ),
                    List.of()
            );
            when(tipoDocumentalRepository.findById(tipoDocumentalId)).thenReturn(Optional.of(mock(co.com.gedsys.base.domain.tipologia.TipoDocumental.class)));
            when(unidadDocumentalRepository.findById(unidadDocumentalId)).thenReturn(Optional.of(mock(co.com.gedsys.base.domain.unidad_documental.UnidadDocumental.class)));
            assertThrows(IllegalArgumentException.class, () -> useCase.execute(command));
        }

        @Test
        @DisplayName("Debe lanzar excepción si el tipo de metadato no corresponde con la definición")
        void deberiaLanzarExcepcionSiTipoDeMetadatoNoCorresponde() {
            var command = new CargarDocumentoCommand(
                    "Título",
                    "usuario1",
                    "file-123",
                    tipoDocumentalId,
                    unidadDocumentalId,
                    List.of(new CargarDocumentoCommand.Metadato("asunto", "valor", co.com.gedsys.base.domain.metadato.enums.TipoMetadatoEnum.GESTION)),
                    List.of()
            );
            var tipoDocumental = mock(co.com.gedsys.base.domain.tipologia.TipoDocumental.class);
            var unidadDocumental = mock(co.com.gedsys.base.domain.unidad_documental.UnidadDocumental.class);
            var definicionMetadato = mock(co.com.gedsys.base.domain.metadato.DefinicionMetadato.class);
            when(tipoDocumentalRepository.findById(tipoDocumentalId)).thenReturn(Optional.of(tipoDocumental));
            when(unidadDocumentalRepository.findById(unidadDocumentalId)).thenReturn(Optional.of(unidadDocumental));
            when(definicionMetadatosRepository.buscarPorPatrones(List.of("asunto"))).thenReturn(Set.of(definicionMetadato));
            when(definicionMetadato.getPatron()).thenReturn("asunto");
            when(definicionMetadato.getTipo()).thenReturn(co.com.gedsys.base.domain.metadato.enums.TipoMetadatoEnum.CONTENIDO);
            assertThrows(IllegalArgumentException.class, () -> useCase.execute(command));
        }
    }

    @Nested
    @DisplayName("Validaciones de campos obligatorios y persistencia")
    class ValidacionesCamposObligatorios {
        @Test
        @DisplayName("Debe lanzar excepción si el título es nulo")
        void deberiaLanzarExcepcionSiTituloEsNulo() {
            var command = new CargarDocumentoCommand(
                    null,
                    "usuario1",
                    "file-123",
                    tipoDocumentalId,
                    unidadDocumentalId,
                    List.of(),
                    List.of()
            );
            assertThrows(NullPointerException.class, () -> useCase.execute(command));
        }

        @Test
        @DisplayName("Debe lanzar excepción si el autor es nulo")
        void deberiaLanzarExcepcionSiAutorEsNulo() {
            var command = new CargarDocumentoCommand(
                    "Título",
                    null,
                    "file-123",
                    tipoDocumentalId,
                    unidadDocumentalId,
                    List.of(),
                    List.of()
            );
            assertThrows(NullPointerException.class, () -> useCase.execute(command));
        }

        @Test
        @DisplayName("Debe lanzar excepción si el fileId es nulo")
        void deberiaLanzarExcepcionSiFileIdEsNulo() {
            var command = new CargarDocumentoCommand(
                    "Título",
                    "usuario1",
                    null,
                    tipoDocumentalId,
                    unidadDocumentalId,
                    List.of(),
                    List.of()
            );
            assertThrows(NullPointerException.class, () -> useCase.execute(command));
        }

        @Test
        @DisplayName("Debe lanzar excepción si la persistencia falla")
        void deberiaLanzarExcepcionSiPersistenciaFalla() {
            var command = new CargarDocumentoCommand(
                    "Título",
                    "usuario1",
                    "file-123",
                    tipoDocumentalId,
                    unidadDocumentalId,
                    List.of(),
                    List.of()
            );
            when(tipoDocumentalRepository.findById(tipoDocumentalId)).thenReturn(Optional.of(mock(co.com.gedsys.base.domain.tipologia.TipoDocumental.class)));
            when(unidadDocumentalRepository.findById(unidadDocumentalId)).thenReturn(Optional.of(mock(co.com.gedsys.base.domain.unidad_documental.UnidadDocumental.class)));
            when(definicionMetadatosRepository.buscarPorPatrones(List.of())).thenReturn(Set.of());
            when(documentoRepository.save(any())).thenThrow(new RuntimeException("Error de persistencia"));
            assertThrows(RuntimeException.class, () -> useCase.execute(command));
        }
    }
}
