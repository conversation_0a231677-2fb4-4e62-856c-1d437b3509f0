package co.com.gedsys.base.application.usecase.planeacion.series_documentales;

import co.com.gedsys.base.adapter.persistence.SerieDocumentalGateway;
import co.com.gedsys.base.adapter.persistence.mappers.SerieDocumentalPersistenceMapper;
import co.com.gedsys.base.application.dto.SerieDocumentalDto;
import co.com.gedsys.base.application.mapper.SerieDocumentalMapper;
import co.com.gedsys.base.domain.documento.DocumentoRepository;
import co.com.gedsys.base.domain.instrumento.ClasificacionDocumentalRepository;
import co.com.gedsys.base.domain.serie_documental.EstadoSerie;
import co.com.gedsys.base.domain.serie_documental.SerieDocumental;
import co.com.gedsys.base.domain.serie_documental.SeriePadreInactivaException;
import co.com.gedsys.base.domain.serie_documental.SerieTieneDependenciasActivasException;
import co.com.gedsys.base.domain.serie_documental.TipoSerie;
import co.com.gedsys.base.domain.unidad_documental.UnidadDocumentalRepository;
import co.com.gedsys.base.infrastructure.data_access.SerieDocumentalEntity;
import co.com.gedsys.base.infrastructure.data_access.repository.SerieDocumentalJpaRepository;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import java.util.Optional;
import java.util.UUID;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class SerieDocumentalIntegrationTest {

    @Mock
    private SerieDocumentalJpaRepository jpaRepository;

    @Mock
    private SerieDocumentalPersistenceMapper persistenceMapper;

    @Mock
    private SerieDocumentalMapper applicationMapper;

    @Mock
    private DocumentoRepository documentoRepository;

    @Mock
    private UnidadDocumentalRepository unidadDocumentalRepository;

    @Mock
    private ClasificacionDocumentalRepository clasificacionDocumentalRepository;

    private SerieDocumentalGateway gateway;
    private ActivarSerieDocumentalUseCase activarUseCase;
    private InactivarSerieDocumentalUseCase inactivarUseCase;

    private UUID serieId;
    private UUID padreId;
    private SerieDocumental serieDocumental;
    private SerieDocumental seriePadre;
    private SerieDocumentalEntity serieEntity;
    private SerieDocumentalEntity padreEntity;
    private SerieDocumentalDto serieDto;

    @BeforeEach
    void setUp() {
        gateway = new SerieDocumentalGateway(jpaRepository, persistenceMapper, documentoRepository, unidadDocumentalRepository, clasificacionDocumentalRepository);
        activarUseCase = new ActivarSerieDocumentalUseCase(gateway, applicationMapper);
        inactivarUseCase = new InactivarSerieDocumentalUseCase(gateway, applicationMapper);

        serieId = UUID.randomUUID();
        padreId = UUID.randomUUID();

        // Configurar entidades de dominio
        serieDocumental = new SerieDocumental("100.01", "Subserie de prueba");
        serieDocumental.setId(serieId);
        serieDocumental.setTipo(TipoSerie.SUBSERIE);
        serieDocumental.setEstado(EstadoSerie.INACTIVA);

        seriePadre = new SerieDocumental("100", "Serie padre");
        seriePadre.setId(padreId);
        seriePadre.setTipo(TipoSerie.SERIE);
        seriePadre.setEstado(EstadoSerie.ACTIVA);

        serieDocumental.setPadre(seriePadre);

        // Configurar entidades JPA
        serieEntity = new SerieDocumentalEntity();
        serieEntity.setId(serieId);
        serieEntity.setCodigo("100.01");
        serieEntity.setNombre("Subserie de prueba");
        serieEntity.setEstado(EstadoSerie.INACTIVA);

        padreEntity = new SerieDocumentalEntity();
        padreEntity.setId(padreId);
        padreEntity.setCodigo("100");
        padreEntity.setNombre("Serie padre");
        padreEntity.setEstado(EstadoSerie.ACTIVA);

        serieEntity.setPadre(padreEntity);

        // Configurar DTO
        serieDto = new SerieDocumentalDto(
            serieId,
            "100.01",
            "Subserie de prueba",
            "SUBSERIE",
            "ACTIVA",
            "100",
            "Serie padre"
        );
    }

    @Test
    @DisplayName("Integración: Debe activar una serie documental correctamente")
    void debeActivarSerieDocumentalCorrectamente() {
        // Arrange
        when(jpaRepository.findById(serieId)).thenReturn(Optional.of(serieEntity));
        when(persistenceMapper.toDomain(serieEntity)).thenReturn(serieDocumental);
        when(jpaRepository.findById(padreId)).thenReturn(Optional.of(padreEntity));
        when(applicationMapper.toDto(any(SerieDocumental.class))).thenReturn(serieDto);
        
        // Act
        SerieDocumentalDto resultado = activarUseCase.ejecutar(new ActivarSerieDocumentalCommand(serieId));
        
        // Assert
        assertEquals(serieDto, resultado);
        assertEquals(EstadoSerie.ACTIVA, serieDocumental.getEstado());
        verify(jpaRepository).save(any(SerieDocumentalEntity.class));
    }

    @Test
    @DisplayName("Integración: Debe fallar al activar una serie con padre inactivo")
    void debeFallarAlActivarSerieConPadreInactivo() {
        // Arrange
        padreEntity.setEstado(EstadoSerie.INACTIVA);
        seriePadre.setEstado(EstadoSerie.INACTIVA);
        
        when(jpaRepository.findById(serieId)).thenReturn(Optional.of(serieEntity));
        when(persistenceMapper.toDomain(serieEntity)).thenReturn(serieDocumental);
        when(jpaRepository.findById(padreId)).thenReturn(Optional.of(padreEntity));
        
        // Act & Assert
        assertThrows(
            SeriePadreInactivaException.class,
            () -> activarUseCase.ejecutar(new ActivarSerieDocumentalCommand(serieId))
        );
        
        verify(jpaRepository, never()).save(any(SerieDocumentalEntity.class));
    }

    @Test
    @DisplayName("Integración: Debe inactivar una serie documental correctamente")
    void debeInactivarSerieDocumentalCorrectamente() {
        // Arrange
        serieDocumental.setEstado(EstadoSerie.ACTIVA);
        serieEntity.setEstado(EstadoSerie.ACTIVA);
        
        when(jpaRepository.findById(serieId)).thenReturn(Optional.of(serieEntity));
        when(persistenceMapper.toDomain(serieEntity)).thenReturn(serieDocumental);
        when(jpaRepository.existsByPadreIdAndEstado(serieId, EstadoSerie.ACTIVA)).thenReturn(false);
        when(applicationMapper.toDto(any(SerieDocumental.class))).thenReturn(serieDto);
        
        // Act
        SerieDocumentalDto resultado = inactivarUseCase.ejecutar(new InactivarSerieDocumentalCommand(serieId));
        
        // Assert
        assertEquals(serieDto, resultado);
        assertEquals(EstadoSerie.INACTIVA, serieDocumental.getEstado());
        verify(jpaRepository).save(any(SerieDocumentalEntity.class));
    }

    @Test
    @DisplayName("Integración: Debe fallar al inactivar una serie con hijas activas")
    void debeFallarAlInactivarSerieConHijasActivas() {
        // Arrange
        serieDocumental.setEstado(EstadoSerie.ACTIVA);
        serieEntity.setEstado(EstadoSerie.ACTIVA);
        
        when(jpaRepository.findById(serieId)).thenReturn(Optional.of(serieEntity));
        when(persistenceMapper.toDomain(serieEntity)).thenReturn(serieDocumental);
        when(jpaRepository.existsByPadreIdAndEstado(serieId, EstadoSerie.ACTIVA)).thenReturn(true);
        
        // Act & Assert
        assertThrows(
            SerieTieneDependenciasActivasException.class,
            () -> inactivarUseCase.ejecutar(new InactivarSerieDocumentalCommand(serieId))
        );
        
        verify(jpaRepository, never()).save(any(SerieDocumentalEntity.class));
    }
}