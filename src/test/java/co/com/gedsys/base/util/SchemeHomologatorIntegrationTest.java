package co.com.gedsys.base.util;

import co.com.gedsys.base.adapter.http.gestion_tramite.envio.SolicitudEnvioDocumento;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import jakarta.validation.ValidationException;
import org.junit.jupiter.api.Test;

import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;

public class SchemeHomologatorIntegrationTest {

    private final ObjectMapper objectMapper = new ObjectMapper();

    @Test
    public void testCurlRequest_SinPropiedadesRadicado_DeberiaFallar() throws Exception {
        // Simular el JSON del curl request original
        String jsonRequest = """
            {
                "documentId":"12fa287e-7076-4fb2-b71a-5e8fefc7123e",
                "emisorUsername": "dplata",
                "destinatarioId": "c7d6e5f4-a3b2-2a1b-6e5f-4d3c2b1a9e8d"
            }
            """;

        Map<String, Object> requestMap = objectMapper.readValue(jsonRequest, new TypeReference<Map<String, Object>>() {});

        SchemeHomologator<SolicitudEnvioDocumento> homologator = 
            new SchemeHomologator<>(SolicitudEnvioDocumento.class);

        // Esto debería fallar ahora con la validación correcta
        ValidationException exception = assertThrows(ValidationException.class, () -> {
            homologator.validateAndConvertToSchema(requestMap);
        });

        System.out.println("Mensaje de error: " + exception.getMessage());
        
        // Verificar que el error menciona las propiedades del radicado
        assertTrue(exception.getMessage().contains("propiedadesRadicado"));
        assertTrue(exception.getMessage().contains("requeridas") || exception.getMessage().contains("required"));
    }

    @Test
    public void testCurlRequest_ConPropiedadesRadicado_DeberiaFuncionar() throws Exception {
        // Simular el JSON con propiedades del radicado
        String jsonRequest = """
            {
                "documentId":"12fa287e-7076-4fb2-b71a-5e8fefc7123e",
                "emisorUsername": "dplata",
                "destinatarioId": "c7d6e5f4-a3b2-2a1b-6e5f-4d3c2b1a9e8d",
                "propiedadesRadicado": {
                    "height": 100,
                    "page": 1,
                    "width": 200,
                    "x": 50,
                    "y": 75,
                    "rotationDegrees": 0
                }
            }
            """;

        Map<String, Object> requestMap = objectMapper.readValue(jsonRequest, new TypeReference<Map<String, Object>>() {});

        SchemeHomologator<SolicitudEnvioDocumento> homologator = 
            new SchemeHomologator<>(SolicitudEnvioDocumento.class);

        // Esto debería funcionar correctamente
        assertDoesNotThrow(() -> {
            SolicitudEnvioDocumento result = homologator.validateAndConvertToSchema(requestMap);
            assertNotNull(result);
            assertNotNull(result.propiedadesRadicado());
            assertEquals(100, result.propiedadesRadicado().height());
            assertEquals(1, result.propiedadesRadicado().page());
            assertEquals(200, result.propiedadesRadicado().width());
            assertEquals(50, result.propiedadesRadicado().x());
            assertEquals(75, result.propiedadesRadicado().y());
            assertEquals(0, result.propiedadesRadicado().rotationDegrees());
        });
    }
}
