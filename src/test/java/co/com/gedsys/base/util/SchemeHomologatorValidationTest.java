package co.com.gedsys.base.util;

import co.com.gedsys.base.adapter.http.gestion_tramite.envio.SolicitudEnvioDocumento;
import jakarta.validation.ValidationException;
import org.junit.jupiter.api.Test;

import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

import static org.junit.jupiter.api.Assertions.*;

public class SchemeHomologatorValidationTest {

    @Test
    public void testSolicitudEnvioDocumento_SinPropiedadesRadicado_DeberiaFallar() {
        SchemeHomologator<SolicitudEnvioDocumento> homologator = 
            new SchemeHomologator<>(SolicitudEnvioDocumento.class);

        Map<String, Object> input = new HashMap<>();
        input.put("documentId", UUID.randomUUID().toString());
        input.put("emisorUsername", "dplata");
        input.put("destinatarioId", UUID.randomUUID().toString());
        // Intencionalmente NO incluir propiedadesRadicado

        ValidationException exception = assertThrows(ValidationException.class, () -> {
            homologator.validateAndConvertToSchema(input);
        });

        assertTrue(exception.getMessage().contains("propiedadesRadicado"));
        assertTrue(exception.getMessage().contains("requeridas"));
    }

    @Test
    public void testSolicitudEnvioDocumento_ConPropiedadesRadicado_DeberiaFuncionar() {
        SchemeHomologator<SolicitudEnvioDocumento> homologator = 
            new SchemeHomologator<>(SolicitudEnvioDocumento.class);

        Map<String, Object> input = new HashMap<>();
        input.put("documentId", UUID.randomUUID().toString());
        input.put("emisorUsername", "dplata");
        input.put("destinatarioId", UUID.randomUUID().toString());
        
        Map<String, Object> propiedades = new HashMap<>();
        propiedades.put("height", 100);
        propiedades.put("page", 1);
        propiedades.put("width", 200);
        propiedades.put("x", 50);
        propiedades.put("y", 75);
        propiedades.put("rotationDegrees", 0);
        input.put("propiedadesRadicado", propiedades);

        assertDoesNotThrow(() -> {
            SolicitudEnvioDocumento result = homologator.validateAndConvertToSchema(input);
            assertNotNull(result);
            assertNotNull(result.propiedadesRadicado());
            assertEquals(100, result.propiedadesRadicado().height());
            assertEquals(1, result.propiedadesRadicado().page());
        });
    }
}
