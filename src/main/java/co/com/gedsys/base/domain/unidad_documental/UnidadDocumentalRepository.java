package co.com.gedsys.base.domain.unidad_documental;

import co.com.gedsys.base.domain.common.DomainRepository;
import co.com.gedsys.base.domain.tipologia.TipoDocumental;

import java.util.List;
import java.util.UUID;

public interface UnidadDocumentalRepository extends DomainRepository<UnidadDocumental, UUID> {
    boolean checkStock(String nombre, UUID clasificacion);

    List<UnidadDocumental> buscarParaTipoDocumental(TipoDocumental tipoDocumental);

    boolean tieneUnidadesDocumentalesAsociadasASerieDocumental(UUID serieId);
}
