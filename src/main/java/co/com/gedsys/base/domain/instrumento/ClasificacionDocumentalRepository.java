package co.com.gedsys.base.domain.instrumento;

import co.com.gedsys.base.domain.common.DomainRepository;
import co.com.gedsys.base.domain.organizacion.Seccion;

import java.util.Optional;
import java.util.UUID;

public interface ClasificacionDocumentalRepository extends DomainRepository<ClasificacionDocumental, UUID> {

    Optional<Seccion> buscarSeccionPorClasificacionId(UUID clasificacionId);

    boolean tieneClasificacionesAsociadasASerieDocumental(UUID serieId);

}
