package co.com.gedsys.base.domain.documento;

import co.com.gedsys.base.domain.common.StringValueObject;

public class NombreAnexo extends StringValueObject {
    public NombreAnexo(String value) {
        super(value);
        judgeNullPolicy();
    }

    @Override
    protected String validate(String value) {
        if (value == null) return null;
        if (value.length() > 255) {
            throw new IllegalArgumentException("El nombre del anexo no puede exceder 255 caracteres");
        }
        // Caracteres no permitidos en Windows: < > : " / \ | ? *
        if (value.matches(".*[<>:\"/\\\\|?*].*")) {
            throw new IllegalArgumentException("El nombre del anexo no puede contener los siguientes caracteres: < > : \" / \\ | ? *");
        }
        // Windows no permite nombres que terminen en punto o espacio
        if (value.endsWith(".") || value.endsWith(" ")) {
            throw new IllegalArgumentException("El nombre del anexo no puede terminar en punto o espacio");
        }
        // Windows reserva ciertos nombres de archivo
        String[] reservedNames = {"CON", "PRN", "AUX", "NUL", "COM1", "COM2", "COM3", "COM4", 
                                 "COM5", "COM6", "COM7", "COM8", "COM9", "LPT1", "LPT2", 
                                 "LPT3", "LPT4", "LPT5", "LPT6", "LPT7", "LPT8", "LPT9"};
        for (String name : reservedNames) {
            if (value.equalsIgnoreCase(name) || value.toUpperCase().startsWith(name + ".")) {
                throw new IllegalArgumentException("El nombre del anexo no puede ser un nombre reservado del sistema");
            }
        }
        return value;
    }
}