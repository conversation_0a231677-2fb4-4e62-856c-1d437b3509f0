package co.com.gedsys.base.application.usecase.gestion_tramite.recepcion_documental;

import co.com.gedsys.base.application.dto.MetadatoDocumentoDTO;
import co.com.gedsys.base.application.dto.PropiedadRadicadoDTO;
import co.com.gedsys.base.application.dto.AnexoDocumentoDTO;

import java.util.List;

public record RadicarDocumentoRecibidoCommand(
        String titulo,
        String fileId,
        String tipoDocumentalId,
        String unidadDocumentalId,
        String autor,
        List<MetadatoDocumentoDTO> metadatos,
        PropiedadRadicadoDTO propiedadRadicado,
        List<AnexoDocumentoDTO> anexos,
        String remitenteId,
        String destinatarioInterno
) {
}
