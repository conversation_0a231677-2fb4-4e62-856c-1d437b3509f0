package co.com.gedsys.base.application.usecase.gestion_tramite;

import co.com.gedsys.base.domain.metadato.enums.TipoMetadatoEnum;
import java.util.List;
import java.util.UUID;

public record CargarDocumentoCommand(
    String titulo,
    String autor,
    String fileId,
    UUID tipoDocumentalId,
    UUID unidadDocumentalId,
    List<Metadato> metadatos,
    List<Anexo> anexos
) {
    public record Metadato(
        String nombre,
        String valor,
        TipoMetadatoEnum tipo
    ) {}

    public record Anexo(
        String nombre,
        String descripcion,
        String fileId,
        String hash,
        Long bytes,
        String extension
    ) {}
}
