package co.com.gedsys.base.adapter.http.gestion_tramite.carga_documental;

import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;

import co.com.gedsys.base.application.usecase.gestion_tramite.CargarDocumentoCommand;

@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.ERROR)
interface AdapterMapperCargaDocumental {

    CargarDocumentoCommand toCommand(SolicitudCargaDocumental request);
}