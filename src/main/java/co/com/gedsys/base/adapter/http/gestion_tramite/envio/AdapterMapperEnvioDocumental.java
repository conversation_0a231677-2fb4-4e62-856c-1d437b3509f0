package co.com.gedsys.base.adapter.http.gestion_tramite.envio;

import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.ReportingPolicy;

import co.com.gedsys.base.adapter.http.produccion.documentos.EstructuraPropiedadesRadicado;
import co.com.gedsys.base.adapter.http.produccion.documentos.MetadadoProduccionDocumental;
import co.com.gedsys.base.application.dto.MetadatoDocumentoDTO;
import co.com.gedsys.base.application.dto.PropiedadRadicadoDTO;
import co.com.gedsys.base.application.usecase.gestion_tramite.envio_documental.RadicarDocumentoEnvioCommand;

@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.ERROR)
public interface AdapterMapperEnvioDocumental {

    @Mapping(target = "destinatarioId", source = "solicitud.destinatarioId")
    @Mapping(target = "metadatos", source = "solicitud.metadatos")
    @Mapping(target = "propiedadRadicado", source = "solicitud.propiedadesRadicado")
    RadicarDocumentoEnvioCommand toRadicarDocumentoEnvioCommand(SolicitudEnvioDocumento solicitud);

    PropiedadRadicadoDTO toPropiedadRadicadoDTO(EstructuraPropiedadesRadicado propiedadesRadicado);

    MetadatoDocumentoDTO toMetadatoDocumentoDTO(MetadadoProduccionDocumental metadato);
}
