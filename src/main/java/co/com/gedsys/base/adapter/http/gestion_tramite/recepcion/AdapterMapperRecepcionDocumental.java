package co.com.gedsys.base.adapter.http.gestion_tramite.recepcion;

import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.ReportingPolicy;

import co.com.gedsys.base.adapter.http.produccion.documentos.EstructuraAnexoDocumento;
import co.com.gedsys.base.adapter.http.produccion.documentos.EstructuraPropiedadesRadicado;
import co.com.gedsys.base.adapter.http.produccion.documentos.MetadadoProduccionDocumental;
import co.com.gedsys.base.application.dto.AnexoDocumentoDTO;
import co.com.gedsys.base.application.dto.MetadatoDocumentoDTO;
import co.com.gedsys.base.application.dto.PropiedadRadicadoDTO;
import co.com.gedsys.base.application.usecase.gestion_tramite.recepcion_documental.RadicarDocumentoRecibidoCommand;

@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.ERROR)
public interface AdapterMapperRecepcionDocumental {

    @Mapping(target = "propiedadRadicado", source = "propiedadesRadicado")
    RadicarDocumentoRecibidoCommand toRadicarDocumentoRecibidoCommand(SolicitudRecepcionDocumento solicitud);

    PropiedadRadicadoDTO toPropiedadRadicadoDTO(EstructuraPropiedadesRadicado propiedadesRadicado);

    AnexoDocumentoDTO toAnexoDocumentoDTO(EstructuraAnexoDocumento anexo);

    MetadatoDocumentoDTO toMetadatoDocumentoDTO(MetadadoProduccionDocumental metadato);
}
