package co.com.gedsys.base.adapter.http.planeacion.usuarios_externos;

import co.com.gedsys.base.application.usecase.planeacion.usuarios_externos.*;
import co.com.gedsys.base.domain.usuario_externo.ExternalUserIdentificationType;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.UUID;

@RequiredArgsConstructor
@RestController
@RequestMapping("/api/v1/planeacion/usuarios-externos")
public class ExternalUserController implements ExternalUserAPIV1 {
    private final CreateExternalUserUseCase createUseCase;
    private final UpdateExternalUserUseCase updateUseCase;
    private final FindExternalUserUseCase findUseCase;
    private final DeleteExternalUserUseCase deleteUseCase;
    private final ListExternalUsersUseCase listUseCase;
    private final AddPropertiesExternalUserUseCase addPropertiesUseCase;

    private final ExternalUsersHttpMapper mapper;

    @Override
    public ExternalUserInfo create(ExternalUserCreateRequest request) {
        // Validación temprana en presentación
        validateIdentificationRequest(request.identificationType(), request.identificationNumber());

        var command = mapper.toCommand(request);
        return mapper.toResponse(createUseCase.execute(command));
    }

    @Override
    public ExternalUserInfo addNewProperties(List<ExternalUserPropertyRegistration> request, UUID id) {
        var command = mapper.toCommand(id, request);
        var updated = addPropertiesUseCase.execute(command);
        return mapper.toResponse(updated);
    }

    @Override
    public ExternalUserInfo update(UUID id, ExternalUserUpdateRequest request) {

        validateIdentificationRequest(request.identificationType(), request.identificationNumber());

        var command = mapper.toCommand(id, request);
        var updated = updateUseCase.execute(command);
        return mapper.toResponse(updated);
    }

    @Override
    public ExternalUserInfo findById(UUID id) {
        return mapper.toResponse(findUseCase.execute(id));
    }

    @Override
    public List<ExternalUserItem> findAll() {
        return listUseCase.execute(null).stream().map(mapper::toItem).toList();
    }

    @Override
    public void delete(UUID id) {
        deleteUseCase.execute(id);
    }

    private void validateIdentificationRequest(ExternalUserIdentificationType type, String number) {
        if (type == ExternalUserIdentificationType.NA && number != null) {
            throw new IllegalArgumentException("Tipo NA no debe incluir número de identificación");
        }
        if (type != ExternalUserIdentificationType.NA && (number == null || number.isBlank())) {
            throw new IllegalArgumentException("Tipo " + type + " requiere número de identificación");
        }
    }

    private void validateIdentificationRequest(String type, String number) {
        try {
            ExternalUserIdentificationType enumType = ExternalUserIdentificationType.valueOf(type);
            validateIdentificationRequest(enumType, number);
        } catch (IllegalArgumentException e) {
            if (e.getMessage().contains("No enum constant")) {
                throw new IllegalArgumentException("Tipo de identificación inválido: " + type);
            }
            throw e;
        }
    }
}
