package co.com.gedsys.base.adapter.persistence;

import co.com.gedsys.base.adapter.persistence.mappers.SerieDocumentalPersistenceMapper;
import co.com.gedsys.base.domain.documento.DocumentoRepository;
import co.com.gedsys.base.domain.instrumento.ClasificacionDocumentalRepository;
import co.com.gedsys.base.domain.serie_documental.EstadoSerie;
import co.com.gedsys.base.domain.serie_documental.SerieDocumental;
import co.com.gedsys.base.domain.serie_documental.SerieDocumentalRepository;
import co.com.gedsys.base.domain.unidad_documental.UnidadDocumentalRepository;
import co.com.gedsys.base.infrastructure.data_access.SerieDocumentalEntity;
import co.com.gedsys.base.infrastructure.data_access.repository.SerieDocumentalJpaRepository;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Example;
import org.springframework.data.domain.ExampleMatcher;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;
import java.util.UUID;

@Repository
@RequiredArgsConstructor
public class SerieDocumentalGateway implements SerieDocumentalRepository {
    private final SerieDocumentalJpaRepository jpaRepository;
    private final SerieDocumentalPersistenceMapper mapper;
    private final DocumentoRepository documentoRepository;
    private final UnidadDocumentalRepository unidadDocumentalRepository;
    private final ClasificacionDocumentalRepository clasificacionDocumentalRepository;

    @Override
    public boolean existenciaPorCodigo(String codigo) {
        var example = SerieDocumentalEntity.builder().codigo(codigo).build();
        return jpaRepository.exists(Example.of(example));
    }

    @Override
    public void guardar(SerieDocumental serieDocumental) {
        var entity = mapper.toEntity(serieDocumental);
        jpaRepository.save(entity);
    }

    @Override
    public Optional<SerieDocumental> findByCode(String codigo) {
        return jpaRepository.findByCodigo(codigo)
                .map(mapper::toDomain);
    }

    @Override
    public Optional<SerieDocumental> findById(UUID id) {
        return jpaRepository.findById(id)
                .map(mapper::toDomain);
    }

    @Override
    public List<SerieDocumental> buscar(EstadoSerie estado) {
        ExampleMatcher matcher = ExampleMatcher.matchingAll();
        Example<SerieDocumentalEntity> example = Example.of(SerieDocumentalEntity.builder().estado(estado).build(), matcher);
        return jpaRepository.findAll(example).stream().map(mapper::toDomain).toList();
    }

    @Override
    public boolean tieneHijasActivas(UUID serieId) {
        return jpaRepository.existsByPadreIdAndEstado(serieId, EstadoSerie.ACTIVA);
    }

    @Override
    public boolean tieneClasificacionesDocumentalesAsociadas(UUID serieId) {
        return clasificacionDocumentalRepository.tieneClasificacionesAsociadasASerieDocumental(serieId);
    }

    @Override
    public boolean tieneDocumentosAsociados(UUID serieId) {
        return documentoRepository.tieneDocumentosAsociadosASerieDocumental(serieId);
    }

    @Override
    public boolean tieneUnidadesDocumentalesAsociadas(UUID serieId) {
        return unidadDocumentalRepository.tieneUnidadesDocumentalesAsociadasASerieDocumental(serieId);
    }

    @Override
    public boolean tienePadreInactivo(UUID serieId) {
        return jpaRepository.findById(serieId)
                .flatMap(hija -> Optional.ofNullable(hija.getPadre()))
                .map(padre -> jpaRepository.findById(padre.getId()))
                .map(padreEntity -> padreEntity.get().getEstado() == EstadoSerie.INACTIVA)
                .orElse(false);
    }
}
