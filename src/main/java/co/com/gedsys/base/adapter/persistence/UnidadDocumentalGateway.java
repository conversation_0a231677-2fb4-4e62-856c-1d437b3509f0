package co.com.gedsys.base.adapter.persistence;

import co.com.gedsys.base.adapter.persistence.mappers.DataAccessUnidadDocumentalMapper;
import co.com.gedsys.base.infrastructure.data_access.repository.UnidadDocumentalJpaRepository;
import co.com.gedsys.base.domain.tipologia.TipoDocumental;
import co.com.gedsys.base.domain.unidad_documental.UnidadDocumental;
import co.com.gedsys.base.domain.unidad_documental.UnidadDocumentalRepository;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;
import java.util.UUID;

@RequiredArgsConstructor
@Repository
public class UnidadDocumentalGateway implements UnidadDocumentalRepository {
    private final UnidadDocumentalJpaRepository jpaRepository;
    private final DataAccessUnidadDocumentalMapper mapper;

    @Override
    public boolean checkStock(String nombre, UUID clasificacion) {
        return jpaRepository.existsByNombreIgnoreCaseAndClasificacion_Id(nombre, clasificacion);
    }

    @Override
    public List<UnidadDocumental> buscarParaTipoDocumental(TipoDocumental tipoDocumental) {
        return jpaRepository
                .queryByClasificacion_TiposDocumentales_Id(tipoDocumental.getId())
                .stream()
                .map(mapper::toDomain)
                .toList();
    }

    @Override
    public UnidadDocumental save(UnidadDocumental unidadDocumental) {
        return mapper.toDomain(jpaRepository.save(mapper.toEntity(unidadDocumental)));
    }

    @Override
    public Optional<UnidadDocumental> findById(UUID uuid) {
        return jpaRepository
                .findById(uuid)
                .map(mapper::toDomain);
    }

    @Override
    public List<UnidadDocumental> findAll() {
        return jpaRepository.findAll()
                .stream()
                .map(mapper::toDomain)
                .toList();
    }

    @Override
    public boolean checkStock(String name) {
        return jpaRepository.existsByNombreIgnoreCase(name);
    }

    @Override
    public boolean checkStock(UnidadDocumental entity) {
        return false;
    }

    @Override
    public void delete(UUID uuid) {

    }

    @Override
    public boolean tieneUnidadesDocumentalesAsociadasASerieDocumental(UUID serieId) {
        return jpaRepository.existsByClasificacion_Subserie_Id(serieId);
    }
}
