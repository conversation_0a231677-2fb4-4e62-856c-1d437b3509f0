package co.com.gedsys.base.adapter.http.gestion_tramite.envio;

import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;

import java.util.ArrayList;
import java.util.List;
import java.util.UUID;

import co.com.gedsys.base.adapter.http.produccion.documentos.EstructuraPropiedadesRadicado;
import co.com.gedsys.base.adapter.http.produccion.documentos.MetadadoProduccionDocumental;

public record SolicitudEnvioDocumento(
        @NotNull(message = "El ID del documento es requerido")
        UUID documentId,
        @NotBlank(message = "El username del usuario emisor es requerido")
        String emisorUsername,
        @NotNull(message = "El destinatario es requerido")
        UUID destinatarioId,
        List<MetadadoProduccionDocumental> metadatos,
        @Valid
        @NotNull(message = "Las propiedades del radicado son requeridas")
        EstructuraPropiedadesRadicado propiedadesRadicado
) {
    public SolicitudEnvioDocumento {
        metadatos = metadatos != null ? metadatos : new ArrayList<>();
    }
}
