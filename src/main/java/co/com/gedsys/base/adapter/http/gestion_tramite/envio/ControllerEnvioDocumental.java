package co.com.gedsys.base.adapter.http.gestion_tramite.envio;

import java.util.Map;

import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import co.com.gedsys.base.application.dto.RadicadoDTO;
import co.com.gedsys.base.application.usecase.gestion_tramite.envio_documental.RadicarDocumentoEnvioDocumentalUseCase;
import co.com.gedsys.base.util.SchemeHomologator;
import io.swagger.v3.oas.annotations.Operation;

@RestController
@RequestMapping("/api/v1/gestion-tramite/envio-documental")
public class ControllerEnvioDocumental {

    private final RadicarDocumentoEnvioDocumentalUseCase enviarDocumentoUseCase;
    private final AdapterMapperEnvioDocumental adapterMapperEnvioDocumental;

    public ControllerEnvioDocumental(RadicarDocumentoEnvioDocumentalUseCase enviarDocumentoUseCase,
            AdapterMapperEnvioDocumental adapterMapperEnvioDocumental) {
        this.enviarDocumentoUseCase = enviarDocumentoUseCase;
        this.adapterMapperEnvioDocumental = adapterMapperEnvioDocumental;
    }

    @Operation(summary = "Registra el envío documental", description = "Envío documental")
    @PostMapping(path = "")
    ResponseEntity<RadicadoDTO> enviarDocumento(
            @RequestBody Map<String, Object> request) {
        var requestValidated = new SchemeHomologator<SolicitudEnvioDocumento>(SolicitudEnvioDocumento.class)
                .validateAndConvertToSchema(request);
        var command = adapterMapperEnvioDocumental.toRadicarDocumentoEnvioCommand(requestValidated);
        return ResponseEntity.ok(enviarDocumentoUseCase.execute(command));
    }

}
