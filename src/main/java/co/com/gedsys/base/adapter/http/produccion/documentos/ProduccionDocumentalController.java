package co.com.gedsys.base.adapter.http.produccion.documentos;

import co.com.gedsys.base.application.dto.DocumentoDTO;

import co.com.gedsys.base.application.dto.RadicadoDTO;
import co.com.gedsys.base.application.usecase.produccion.documental.*;
import co.com.gedsys.base.application.usecase.produccion.documental.command.*;
import co.com.gedsys.base.application.usecase.produccion.radicacion.RadicarDocumentoProducidoUseCase;
import co.com.gedsys.base.application.usecase.produccion.radicacion.command.RadicarDocumentoProducidoCommand;
import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RestController;


import java.util.UUID;

@RestController
@RequiredArgsConstructor
public class ProduccionDocumentalController implements ProduccionDocumentosAPI {

    private final RegistrarBorradorUseCase createUseCase;
    private final ActualizarBorradorUseCase actualizarBorradorUseCase;
    private final FirmarDocumentoUseCase firmarDocumentoUseCase;
    private final RechazarFirmaUseCase rechazarFirmaDocumentoUseCase;
    private final AprobarDocumentoUseCase aprobarDocumentoUseCase;
    private final DesaprobarDocumentoUseCase desaprobarDocumentoUseCase;
    @org.springframework.beans.factory.annotation.Qualifier("produccionDocumentosHttpMapperImpl")
    private final ProduccionDocumentosHttpMapper mapper;
    private final RadicarDocumentoProducidoUseCase radicarDocumentoProducidoUseCase;
    private final AgregarAnexoUseCase agregarAnexoUseCase;

    @Override
    public RespuestaDocumentoProducido crearBorrador(SolicitudRegistroDocumento request) {
        var command = mapper.toCommand(request);
        var document = createUseCase.execute(command);
        return mapper.toResponse(document);
    }

    @Override
    public ResponseEntity<RespuestaDocumentoProducido> actualizarBorrador(UUID id,
            SolicitudActualizacionDocumento request) {
        var command = new UpdateDraftCommand(
                id,
                request.titulo(),
                request.metadatos(),
                request.anexos().stream()
                        .map(mapper::toCommandAnexo)
                        .toList());
        var documento = actualizarBorradorUseCase.execute(command);
        return ResponseEntity.ok(mapper.toResponse(documento));
    }

    @Override
    public ResponseEntity<Void> firmarDocumento(UUID id, String firmante) {
        firmarDocumentoUseCase.execute(new FirmarDocumentoCommand(id, firmante));
        return ResponseEntity.noContent().build();
    }

    @Override
    public ResponseEntity<Void> rechazarFirmaDocumento(UUID id, SolicitudRechazoFirmaDocumento request,
            String firmante) {
        rechazarFirmaDocumentoUseCase.execute(new RechazarFirmaCommand(id, firmante, request.observaciones()));
        return ResponseEntity.noContent().build();
    }

    @Override
    public ResponseEntity<Void> aprobarDocumento(UUID id, String aprobador) {
        aprobarDocumentoUseCase.execute(new AprobarDocumentoCommand(id, aprobador));
        return ResponseEntity.noContent().build();
    }

    @Override
    public ResponseEntity<Void> desaprobarDocumento(UUID id, SolicitudDesaprobarDocumento request, String aprobador) {
        desaprobarDocumentoUseCase.execute(new DesaprobarDocumentoCommand(id, aprobador, request.observaciones()));
        return ResponseEntity.noContent().build();
    }

    @Override
    public ResponseEntity<RadicadoDTO> radicarDocumento(UUID documentId) {
        var command = new RadicarDocumentoProducidoCommand(documentId);
        var radicado = radicarDocumentoProducidoUseCase.execute(command);
        return ResponseEntity.ok(radicado);
    }



    @Override
    public ResponseEntity<DocumentoDTO> agregarAnexo(UUID documentId, SolicitudAgregarAnexo request) {
        var command = new AgregarAnexoCommand(
                documentId,
                request.nombre(),
                request.descripcion(),
                request.fileId(),
                request.hash(),
                request.bytes(),
                request.extension()
        );
        var documentoActualizado = agregarAnexoUseCase.execute(command);
        return ResponseEntity.ok(documentoActualizado);
    }

}
