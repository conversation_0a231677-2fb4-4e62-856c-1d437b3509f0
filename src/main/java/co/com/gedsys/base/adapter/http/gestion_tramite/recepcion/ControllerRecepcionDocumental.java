package co.com.gedsys.base.adapter.http.gestion_tramite.recepcion;

import java.util.Map;

import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import co.com.gedsys.base.application.dto.RadicadoDTO;
import co.com.gedsys.base.application.usecase.gestion_tramite.recepcion_documental.RadicarDocumentoRecibidoUseCase;
import co.com.gedsys.base.util.SchemeHomologator;
import io.swagger.v3.oas.annotations.Operation;

@RestController
@RequestMapping("/api/v1/gestion-tramite/recepcion-documental")
public class ControllerRecepcionDocumental {

    private final RadicarDocumentoRecibidoUseCase recibirDocumentoUseCase;
    private final AdapterMapperRecepcionDocumental adapterMapperRecepcionDocumental;

    public ControllerRecepcionDocumental(RadicarDocumentoRecibidoUseCase recibirDocumentoUseCase,
            AdapterMapperRecepcionDocumental adapterMapperRecepcionDocumental) {
        this.recibirDocumentoUseCase = recibirDocumentoUseCase;
        this.adapterMapperRecepcionDocumental = adapterMapperRecepcionDocumental;
    }

    @Operation(summary = "Registra la recepción documental", description = "Recepcion documental")
    @PostMapping(path = "")
    ResponseEntity<RadicadoDTO> recibirDocumento(@RequestBody Map<String, Object> request) {
        var requestValidated = new SchemeHomologator<SolicitudRecepcionDocumento>(SolicitudRecepcionDocumento.class)
                .validateAndConvertToSchema(request);
        var command = adapterMapperRecepcionDocumental.toRadicarDocumentoRecibidoCommand(requestValidated);
        return ResponseEntity.ok(recibirDocumentoUseCase.execute(command));
    }

}
