package co.com.gedsys.base.adapter.http.produccion.documentos;

import co.com.gedsys.base.application.dto.DocumentoDTO;
import co.com.gedsys.base.application.dto.MetadatoDocumentoDTO;
import co.com.gedsys.base.application.usecase.gestion_tramite.CargarDocumentoCommand;
import co.com.gedsys.base.application.usecase.produccion.documental.command.RegistrarBorradorCommand;
import co.com.gedsys.base.application.usecase.produccion.documental.command.UpdateDraftCommand;

import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.ReportingPolicy;

import java.util.List;

@Mapper(componentModel = "spring",
        unmappedTargetPolicy = ReportingPolicy.ERROR)
public interface ProduccionDocumentosHttpMapper {

    @Mapping(target = "unidadDocumentalNombre", source = "unidadDocumental.nombre")
    @Mapping(target = "unidadDocumentalId", source = "unidadDocumental.id")
    @Mapping(target = "tipoDocumentalNombre", source = "tipoDocumental.nombre")
    @Mapping(target = "tipoDocumentalId", source = "tipoDocumental.id")
    @Mapping(target = "codigoClasificatorio", source = "unidadDocumental.codigoClasificacion")
    @Mapping(target = "fileId", source = "fileId")
    RespuestaDocumentoProducido toResponse(DocumentoDTO documentoDTO);

    @Mapping(target = "owner", source = "firmante")
    RespuestaDocumentoProducido.FirmaDS toResponse(DocumentoDTO.FirmaDS firma);

    RespuestaDocumentoProducido.AnexoDS toResponse(DocumentoDTO.AnexoDS anexo);

    RegistrarBorradorCommand toCommand(SolicitudRegistroDocumento request);

    UpdateDraftCommand.RegistroAnexo toCommandAnexo(SolicitudActualizacionDocumento.RegistroAnexo anexo);

    @Mapping(target = "id", ignore = true)
    DocumentoDTO.AnexoDS toAnexoDTO(UpdateDraftCommand.RegistroAnexo anexo);

    SolicitudActualizacionDocumento.RegistroAnexo toResponseAnexo(DocumentoDTO.AnexoDS anexo);

    MetadatoDocumentoDTO toMetadatoDTO(MetadadoProduccionDocumental metadato);

    List<MetadatoDocumentoDTO> toMetadatosDTO(List<MetadadoProduccionDocumental> metadatos);

}
