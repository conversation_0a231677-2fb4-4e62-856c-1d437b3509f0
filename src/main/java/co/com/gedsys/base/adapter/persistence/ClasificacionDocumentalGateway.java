package co.com.gedsys.base.adapter.persistence;

import co.com.gedsys.base.adapter.persistence.mappers.DataAccessClasificacionDocumentalMapper;
import co.com.gedsys.base.adapter.persistence.mappers.SeccionPersistenceMapper;
import co.com.gedsys.base.infrastructure.data_access.repository.ClasificacionDocumentalJpaRepository;
import co.com.gedsys.base.domain.instrumento.ClasificacionDocumental;
import co.com.gedsys.base.domain.instrumento.ClasificacionDocumentalRepository;
import co.com.gedsys.base.domain.organizacion.Seccion;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;
import java.util.UUID;

@RequiredArgsConstructor
@Repository
public class ClasificacionDocumentalGateway implements ClasificacionDocumentalRepository {
    private final ClasificacionDocumentalJpaRepository jpaRepository;
    private final DataAccessClasificacionDocumentalMapper mapper;
    private final SeccionPersistenceMapper seccionMapper;

    @Override
    public ClasificacionDocumental save(ClasificacionDocumental domainEntity) {
        var entity = mapper.toEntity(domainEntity);
        return mapper.toDomain(jpaRepository.save(entity));
    }

    @Override
    public Optional<ClasificacionDocumental> findById(UUID uuid) {
        return Optional.ofNullable(uuid)
                .flatMap(jpaRepository::findById)
                .map(mapper::toDomain);
    }

    @Override
    public List<ClasificacionDocumental> findAll() {
        return mapper.toDomainList(jpaRepository.findAll());
    }

    @Override
    public boolean checkStock(String name) {
        return false;
    }

    @Override
    public boolean checkStock(ClasificacionDocumental entity) {
        return jpaRepository.existsBySeccion_CodigoAndSubserie_Codigo(entity.getCodigoSeccion(), entity.getCodigoSubSerie());
    }

    @Override
    public void delete(UUID uuid) {

    }

    @Override
    public Optional<Seccion> buscarSeccionPorClasificacionId(UUID clasificacionId) {
        if (clasificacionId == null) {
            return Optional.empty();
        }

        return jpaRepository.findById(clasificacionId)
                .map(entity -> entity.getSeccion())
                .map(seccionMapper::toDomain);
    }

    @Override
    public boolean tieneClasificacionesAsociadasASerieDocumental(UUID serieId) {
        return jpaRepository.existsBySubserie_Id(serieId);
    }

}
