package co.com.gedsys.base.adapter.http.gestion_tramite.carga_documental;

import co.com.gedsys.base.application.dto.DocumentoDTO;
import co.com.gedsys.base.application.usecase.gestion_tramite.CargarDocumentoUseCase;
import co.com.gedsys.base.util.SchemeHomologator;
import java.util.Map;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import jakarta.validation.Valid;

@RestController
@RequestMapping("/api/v1/gestion-tramite/carga-documental")
public class ControllerCargaDocumental {
    private final CargarDocumentoUseCase cargarDocumentoUseCase;
    private final AdapterMapperCargaDocumental mapper;
    private final SchemeHomologator<SolicitudCargaDocumental> schemeHomologator;

    public ControllerCargaDocumental(CargarDocumentoUseCase cargarDocumentoUseCase,
            AdapterMapperCargaDocumental mapper) {
        this.mapper = mapper;
        this.cargarDocumentoUseCase = cargarDocumentoUseCase;
        this.schemeHomologator = new SchemeHomologator<>(SolicitudCargaDocumental.class);
    }

    @Operation(summary = "Cargar documento")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "201", description = "Documento cargado exitosamente"),
            @ApiResponse(responseCode = "400", description = "Datos inválidos")
    })
    @PostMapping
    ResponseEntity<DocumentoDTO> cargarDocumento(@Valid @RequestBody Map<String, Object> request) {
        var documento = cargarDocumentoUseCase.execute(
                mapper.toCommand(schemeHomologator.validateAndConvertToSchema(request)));
        return ResponseEntity.status(HttpStatus.CREATED).body(documento);
    }
}