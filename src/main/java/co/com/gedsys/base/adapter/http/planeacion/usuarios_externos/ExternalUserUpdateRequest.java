package co.com.gedsys.base.adapter.http.planeacion.usuarios_externos;

import java.io.Serializable;
import java.util.List;

import co.com.gedsys.base.domain.usuario_externo.ExternalUserIdentificationType;

public record ExternalUserUpdateRequest(String name, String salutation, String identificationType,
        String identificationNumber, String notes, ExternalUserStatus status,
        List<ExternalUserProperty> properties) implements Serializable {
    public ExternalUserUpdateRequest {
        if (identificationType == null) {
            identificationType = ExternalUserIdentificationType.NA.name();
        }
    }
}
