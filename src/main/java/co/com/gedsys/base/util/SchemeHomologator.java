package co.com.gedsys.base.util;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import jakarta.validation.*;

import java.lang.reflect.Field;
import java.lang.reflect.ParameterizedType;
import java.lang.reflect.Type;
import java.util.*;

public class SchemeHomologator<T> {
    private final ObjectMapper objectMapper;
    private final Validator validator;
    private Class<T> schemaClass;

    public SchemeHomologator(ObjectMapper objectMapper, Validator validator) {
        this.objectMapper = objectMapper;
        this.validator = validator;
    }

    public SchemeHomologator(Class<T> schemaClass) {
        this.objectMapper = new ObjectMapper();
        ValidatorFactory factory = Validation.buildDefaultValidatorFactory();
        this.validator = factory.getValidator();
        this.schemaClass = schemaClass;
    }

    public SchemeHomologator<T> withSchemaClass(Class<T> schemaClass) {
        this.schemaClass = schemaClass;
        return this;
    }

    public T validateAndConvertToSchema(Map<String, Object> inputObject) {
        try {
            JsonNode inputNode = objectMapper.valueToTree(inputObject);
            Map<String, JsonNode> flattenedData = flattenJsonNode(inputNode);

            Map<String, Object> resultMap = createMapFromSchema(schemaClass, flattenedData);
            T result = objectMapper.convertValue(resultMap, schemaClass);

            Set<ConstraintViolation<T>> violations = validator.validate(result);
            if (!violations.isEmpty()) {
                StringBuilder sb = new StringBuilder("Errores de validación:\n");
                for (ConstraintViolation<T> violation : violations) {
                    sb.append("- ").append(violation.getPropertyPath())
                      .append(": ").append(violation.getMessage()).append("\n");
                }
                throw new ValidationException(sb.toString());
            }

            return result;
        } catch (ValidationException e) {
            throw e;
        } catch (Exception e) {
            throw new RuntimeException("Error al validar y convertir el objeto", e);
        }
    }

    private Map<String, JsonNode> flattenJsonNode(JsonNode node) {
        Map<String, JsonNode> flattened = new HashMap<>();
        flattenRecursive(node, flattened);
        return flattened;
    }

    private void flattenRecursive(JsonNode node, Map<String, JsonNode> result) {
        if (node.isObject()) {
            Iterator<Map.Entry<String, JsonNode>> fields = node.fields();
            while (fields.hasNext()) {
                Map.Entry<String, JsonNode> field = fields.next();
                String key = field.getKey();
                JsonNode value = field.getValue();

                // Siempre guardar el valor en el nivel actual
                result.put(key, value);

                // Hacer recursión solo para objetos que no son records del esquema objetivo
                if (value.isObject() && !isSchemaField(key)) {
                    flattenRecursive(value, result);
                }
            }
        }
    }

    private boolean isSchemaField(String fieldName) {
        if (schemaClass == null) return false;

        Field[] fields = schemaClass.getDeclaredFields();
        for (Field field : fields) {
            if (field.getName().equals(fieldName)) {
                return true;
            }
        }
        return false;
    }

    private boolean isFieldRequired(Field field) {
        return field.isAnnotationPresent(jakarta.validation.constraints.NotNull.class);
    }

    private Map<String, Object> createMapFromSchema(Class<?> clazz, Map<String, JsonNode> flattenedData) {
        Map<String, Object> result = new HashMap<>();
        Field[] fields = clazz.getDeclaredFields();

        for (Field field : fields) {
            String fieldName = field.getName();
            Class<?> fieldType = field.getType();
            JsonNode valueNode = flattenedData.get(fieldName);

            Object finalValue = null;

            if (valueNode != null) {
                if (fieldType.isRecord()) {
                    if (valueNode.isObject()) {
                        // Para records anidados, procesar directamente el objeto sin aplanar
                        finalValue = objectMapper.convertValue(valueNode, fieldType);
                    }
                } else if (List.class.isAssignableFrom(fieldType)) {
                    finalValue = processListField(field, valueNode);
                } else {
                    finalValue = objectMapper.convertValue(valueNode, fieldType);
                }
            } else if (fieldType.isRecord()) {
                // Solo crear objeto vacío si el campo NO es requerido (@NotNull)
                if (!isFieldRequired(field)) {
                    finalValue = createMapFromSchema(fieldType, new HashMap<>());
                }
                // Si es requerido, dejar finalValue como null para que falle la validación @NotNull
            } else if (List.class.isAssignableFrom(fieldType)) {
                finalValue = new ArrayList<>();
            }

            result.put(fieldName, finalValue);
        }

        return result;
    }

    private Object processListField(Field field, JsonNode valueNode) {
        if (!valueNode.isArray()) {
            return new ArrayList<>();
        }

        Type genericType = field.getGenericType();
        if (!(genericType instanceof ParameterizedType)) {
            return objectMapper.convertValue(valueNode, List.class);
        }

        ParameterizedType paramType = (ParameterizedType) genericType;
        Class<?> itemType = (Class<?>) paramType.getActualTypeArguments()[0];

        if (itemType.isRecord()) {
            List<Object> list = new ArrayList<>();
            for (JsonNode item : valueNode) {
                if (item.isObject()) {
                    // Para records en listas, convertir directamente sin aplanar
                    list.add(objectMapper.convertValue(item, itemType));
                }
            }
            return list;
        } else {
            return objectMapper.convertValue(valueNode, List.class);
        }
    }
}


